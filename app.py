from flask import Flask, render_template, request, send_file
from utils.ocr import extract_text
from utils.translator import translate_text
from PIL import Image, ImageDraw, ImageFont
import os

app = Flask(__name__)
app.config['OUTPUT_FOLDER'] = 'output'
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        file = request.files.get('image')
        if not file:
            return "没有上传图片"

        image = Image.open(file.stream).convert("RGB")
        results = extract_text(image)

        draw = ImageDraw.Draw(image)
        font = ImageFont.truetype("arial.ttf", size=20)

        for (box, text) in results:
            translated = translate_text(text, target_lang="zh-CN")
            x, y = box[0]
            draw.text((x, y), translated, fill="red", font=font)

        output_path = os.path.join(app.config['OUTPUT_FOLDER'], 'translated_image.png')
        image.save(output_path)

        return send_file(output_path, mimetype='image/png')

    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True)
