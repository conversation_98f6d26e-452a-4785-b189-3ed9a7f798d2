#!/usr/bin/env python3
"""
简单测试脚本：验证基本功能
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_get_request():
    """测试GET请求"""
    print("测试GET请求...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"内容类型: {response.headers.get('content-type')}")
        print(f"响应长度: {len(response.text)}")
        if response.status_code == 200:
            print("✓ GET请求成功")
        else:
            print("✗ GET请求失败")
    except Exception as e:
        print(f"✗ GET请求异常: {e}")

def test_post_no_file():
    """测试POST请求但没有文件"""
    print("\n测试POST请求（无文件）...")
    try:
        response = requests.post(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 400:
            try:
                data = response.json()
                if "error" in data:
                    print("✓ 正确返回错误信息")
                else:
                    print("✗ 响应格式不正确")
            except:
                print("✗ 响应不是JSON格式")
        else:
            print("✗ 状态码不正确")
    except Exception as e:
        print(f"✗ POST请求异常: {e}")

def test_post_invalid_file():
    """测试POST请求但文件格式无效"""
    print("\n测试POST请求（无效文件格式）...")
    try:
        files = {'image': ('test.txt', 'This is not an image', 'text/plain')}
        response = requests.post(f"{BASE_URL}/", files=files)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 400:
            try:
                data = response.json()
                if "error" in data and "格式" in data["error"]:
                    print("✓ 正确识别无效文件格式")
                else:
                    print("✗ 错误信息不正确")
            except:
                print("✗ 响应不是JSON格式")
        else:
            print("✗ 状态码不正确")
    except Exception as e:
        print(f"✗ POST请求异常: {e}")

if __name__ == "__main__":
    print("开始简单测试...")
    test_get_request()
    test_post_no_file()
    test_post_invalid_file()
    print("\n测试完成！")
