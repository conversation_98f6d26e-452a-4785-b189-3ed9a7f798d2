#!/usr/bin/env python3
"""
测试脚本：验证图片翻译Web应用的各种异常情况处理
"""

import requests
import os
import logging
from PIL import Image
import io

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试服务器地址
BASE_URL = "http://localhost:5000"

def create_test_image(width=100, height=100, color=(255, 255, 255)):
    """创建测试图片"""
    image = Image.new('RGB', (width, height), color)
    return image

def image_to_bytes(image, format='PNG'):
    """将PIL图像转换为字节流"""
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format=format)
    img_byte_arr.seek(0)
    return img_byte_arr

def test_no_file():
    """测试：没有上传文件"""
    logger.info("测试：没有上传文件")
    try:
        response = requests.post(f"{BASE_URL}/")
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_invalid_file_format():
    """测试：无效文件格式"""
    logger.info("测试：无效文件格式")
    try:
        # 创建一个文本文件
        files = {'image': ('test.txt', 'This is not an image', 'text/plain')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_small_image():
    """测试：图片尺寸过小"""
    logger.info("测试：图片尺寸过小")
    try:
        # 创建一个5x5的小图片
        image = create_test_image(5, 5)
        img_bytes = image_to_bytes(image)
        
        files = {'image': ('small.png', img_bytes, 'image/png')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_empty_image():
    """测试：空白图片（无文字）"""
    logger.info("测试：空白图片（无文字）")
    try:
        # 创建一个空白图片
        image = create_test_image(200, 200, (255, 255, 255))
        img_bytes = image_to_bytes(image)
        
        files = {'image': ('blank.png', img_bytes, 'image/png')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_corrupted_image():
    """测试：损坏的图片文件"""
    logger.info("测试：损坏的图片文件")
    try:
        # 创建一个假的PNG文件（只有PNG头部）
        fake_png = b'\x89PNG\r\n\x1a\n' + b'corrupted data'
        
        files = {'image': ('corrupted.png', fake_png, 'image/png')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_large_image():
    """测试：大尺寸图片"""
    logger.info("测试：大尺寸图片")
    try:
        # 创建一个较大的图片
        image = create_test_image(2000, 2000)
        img_bytes = image_to_bytes(image)
        
        files = {'image': ('large.png', img_bytes, 'image/png')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
    except Exception as e:
        logger.error(f"请求失败: {e}")

def test_normal_image_with_text():
    """测试：包含文字的正常图片"""
    logger.info("测试：包含文字的正常图片")
    try:
        # 创建一个包含文字的图片
        from PIL import ImageDraw, ImageFont
        
        image = create_test_image(400, 200, (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        draw.text((50, 50), "Hello World", fill=(0, 0, 0), font=font)
        draw.text((50, 100), "Test Image", fill=(0, 0, 0), font=font)
        
        img_bytes = image_to_bytes(image)
        
        files = {'image': ('text_image.png', img_bytes, 'image/png')}
        response = requests.post(f"{BASE_URL}/", files=files)
        logger.info(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 保存返回的图片
            with open('test_output.png', 'wb') as f:
                f.write(response.content)
            logger.info("成功保存翻译后的图片: test_output.png")
        else:
            logger.info(f"响应内容: {response.text}")
            
    except Exception as e:
        logger.error(f"请求失败: {e}")

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有测试...")
    
    tests = [
        test_no_file,
        test_invalid_file_format,
        test_small_image,
        test_empty_image,
        test_corrupted_image,
        test_large_image,
        test_normal_image_with_text
    ]
    
    for test_func in tests:
        try:
            test_func()
            logger.info("-" * 50)
        except Exception as e:
            logger.error(f"测试 {test_func.__name__} 失败: {e}")
            logger.info("-" * 50)

if __name__ == "__main__":
    print("请确保Flask应用正在运行 (python app.py)")
    print("然后按Enter键开始测试...")
    input()
    
    run_all_tests()
    
    print("\n测试完成！请检查日志输出和生成的test_output.png文件。")
