import easyocr
import logging
import numpy as np
from PIL import Image

logger = logging.getLogger(__name__)

# 全局初始化reader，避免重复加载模型
reader = None

def get_reader():
    """获取OCR读取器，延迟初始化"""
    global reader
    if reader is None:
        try:
            logger.info("初始化EasyOCR读取器...")
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 简体中文 + 英文
            logger.info("EasyOCR读取器初始化成功")
        except Exception as e:
            logger.error(f"EasyOCR读取器初始化失败: {str(e)}")
            raise
    return reader

def extract_text(pil_image):
    """
    从PIL图像中提取文字

    Args:
        pil_image: PIL Image对象

    Returns:
        list: [(box, text), ...] 格式的结果列表

    Raises:
        Exception: OCR处理失败时抛出异常
    """
    try:
        if not isinstance(pil_image, Image.Image):
            raise ValueError("输入必须是PIL Image对象")

        # 检查图像尺寸
        width, height = pil_image.size
        if width < 10 or height < 10:
            logger.warning(f"图像尺寸过小: {width}x{height}")
            return []

        if width > 4000 or height > 4000:
            logger.info(f"图像尺寸较大: {width}x{height}，可能需要较长处理时间")

        # 转换为numpy数组
        image_array = np.array(pil_image)
        logger.info(f"开始OCR识别，图像尺寸: {width}x{height}")

        # 获取reader并进行OCR识别
        ocr_reader = get_reader()
        results = ocr_reader.readtext(image_array)

        if not results:
            logger.info("OCR未识别到任何文字")
            return []

        # 处理结果，过滤置信度过低的结果
        processed_results = []
        for item in results:
            try:
                if len(item) >= 3:
                    box, text, confidence = item

                    # 过滤置信度过低的结果
                    if confidence < 0.3:
                        logger.debug(f"跳过低置信度文本: {text} (置信度: {confidence:.2f})")
                        continue

                    # 过滤空文本或只包含空白字符的文本
                    if not text or not text.strip():
                        logger.debug("跳过空文本")
                        continue

                    # 验证box格式
                    if not isinstance(box, (list, tuple)) or len(box) < 4:
                        logger.warning(f"无效的box格式: {box}")
                        continue

                    processed_results.append((box, text.strip()))
                    logger.debug(f"识别到文本: {text.strip()} (置信度: {confidence:.2f})")
                else:
                    logger.warning(f"OCR结果格式异常: {item}")

            except Exception as e:
                logger.error(f"处理OCR结果项时出错: {str(e)}, 项目: {item}")
                continue

        logger.info(f"OCR识别完成，有效文本区域: {len(processed_results)}")
        return processed_results

    except Exception as e:
        logger.error(f"OCR识别过程中出错: {str(e)}")
        raise Exception(f"OCR识别失败: {str(e)}")
